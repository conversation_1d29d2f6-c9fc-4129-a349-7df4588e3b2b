// API请求封装
import config from "@/config";
const BASE_URL = config.baseURL;

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync("token");

    // 设置请求头
    const header = {
      "Content-Type": "application/json",
      ...options.header,
    };

    // 如果有token，添加到请求头
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    uni.request({
      url: BASE_URL + options.url,
      method: options.method || "GET",
      data: options.data || {},
      header,
      success: (res) => {
        // 统一处理响应
        if (res.statusCode === 200) {
          const data = res.data;
          if (data.code === 200) {
            resolve(data);
          } else {
            // 业务错误
            uni.showToast({
              title: data.msg || "请求失败",
              icon: "none",
            });
            reject(data);
          }
        } else if (res.statusCode === 401) {
          // token过期，跳转登录
          uni.removeStorageSync("token");
          uni.removeStorageSync("userInfo");
          uni.reLaunch({
            url: "/pages/login/login",
          });
          reject(res);
        } else {
          // 其他错误
          uni.showToast({
            title: "网络错误",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// GET请求
export const get = (url, data = {}) => {
  return request({
    url,
    method: "GET",
    data,
  });
};

// POST请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: "POST",
    data,
  });
};

// PUT请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: "PUT",
    data,
  });
};

// DELETE请求
export const del = (url, data = {}) => {
  return request({
    url,
    method: "DELETE",
    data,
  });
};

// 文件上传
export const upload = (url, filePath, name = "file") => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync("token");

    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name,
      header: {
        Authorization: token ? `Bearer ${token}` : "",
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        if (data.code === 200) {
          resolve(data);
        } else {
          uni.showToast({
            title: data.msg || "上传失败",
            icon: "none",
          });
          reject(data);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

export default request;
