<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮光壁垒登录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .preview-container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            height: 667px;
        }
        
        .phone-frame {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }
        
        .bg-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }
        
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }
        
        .circle-1 {
            width: 100px;
            height: 100px;
            top: 10%;
            right: -25px;
        }
        
        .circle-2 {
            width: 75px;
            height: 75px;
            top: 60%;
            left: -15px;
            animation-duration: 8s;
            animation-direction: reverse;
        }
        
        .circle-3 {
            width: 50px;
            height: 50px;
            top: 30%;
            left: 20%;
            animation-duration: 10s;
        }
        
        .content {
            position: relative;
            z-index: 10;
            padding: 40px 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #fff;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #667eea;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: glow 3s ease-in-out infinite alternate;
        }
        
        .title {
            font-size: 26px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 8px;
            text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 10px;
        }
        
        .title-underline {
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fff, transparent);
            margin: 0 auto;
            border-radius: 1px;
        }
        
        .form-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 5px;
        }
        
        .form-desc {
            font-size: 13px;
            color: #666;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }
        
        .input-wrapper:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        
        .input-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 16px;
        }
        
        .input-field {
            width: 100%;
            padding: 12px 12px 12px 35px;
            border: none;
            background: transparent;
            font-size: 14px;
            outline: none;
        }
        
        .forgot-password {
            text-align: right;
            margin-bottom: 15px;
        }
        
        .link {
            color: #667eea;
            text-decoration: none;
            font-size: 13px;
        }
        
        .agreement {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 12px;
            color: #666;
            flex-wrap: wrap;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }
        
        .divider-text {
            margin: 0 10px;
            font-size: 12px;
            color: #999;
        }
        
        .other-login {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }
        
        .other-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
        }
        
        .other-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5px;
            font-size: 18px;
        }
        
        .wechat-icon {
            background: rgba(7, 193, 96, 0.1);
            color: #07c160;
            border: 1px solid rgba(7, 193, 96, 0.2);
        }
        
        .guest-icon {
            background: rgba(153, 153, 153, 0.1);
            color: #999;
            border: 1px solid rgba(153, 153, 153, 0.2);
        }
        
        .other-text {
            font-size: 12px;
            color: #666;
        }
        
        .footer-links {
            text-align: center;
            font-size: 13px;
        }
        
        .footer-text {
            color: #666;
            margin-right: 5px;
        }
        
        .register-link {
            color: #667eea;
            font-weight: bold;
            text-decoration: none;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes glow {
            0% { box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); }
            100% { box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4); }
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .info-list {
            list-style: none;
            padding: 0;
        }
        
        .info-list li {
            padding: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        .info-list li::before {
            content: "✨";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="phone-frame">
            <div class="bg-decoration">
                <div class="bg-circle circle-1"></div>
                <div class="bg-circle circle-2"></div>
                <div class="bg-circle circle-3"></div>
            </div>
            
            <div class="content">
                <div class="header">
                    <div class="logo">浮</div>
                    <div class="title">浮光壁垒</div>
                    <div class="subtitle">链接你我，共创未来</div>
                    <div class="title-underline"></div>
                </div>
                
                <div class="form-container">
                    <div class="form-title">欢迎回来</div>
                    <div class="form-desc">请登录您的账户</div>
                    
                    <div class="input-group">
                        <div class="input-wrapper">
                            <div class="input-icon">👤</div>
                            <input type="text" class="input-field" placeholder="请输入用户名/手机号">
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <div class="input-wrapper">
                            <div class="input-icon">🔒</div>
                            <input type="password" class="input-field" placeholder="请输入密码">
                        </div>
                    </div>
                    
                    <div class="forgot-password">
                        <a href="#" class="link">忘记密码？</a>
                    </div>
                    
                    <div class="agreement">
                        <input type="checkbox" id="agree" style="margin-right: 5px;">
                        <label for="agree" style="margin-right: 5px;">我已阅读并同意</label>
                        <a href="#" class="link">《用户协议》</a>
                        <span style="margin: 0 3px;">和</span>
                        <a href="#" class="link">《隐私政策》</a>
                    </div>
                    
                    <button class="login-btn">登录</button>
                    
                    <div class="divider">
                        <div class="divider-line"></div>
                        <div class="divider-text">或</div>
                        <div class="divider-line"></div>
                    </div>
                    
                    <div class="other-login">
                        <div class="other-item">
                            <div class="other-icon wechat-icon">💬</div>
                            <div class="other-text">微信登录</div>
                        </div>
                        <div class="other-item">
                            <div class="other-icon guest-icon">👤</div>
                            <div class="other-text">游客模式</div>
                        </div>
                    </div>
                    
                    <div class="footer-links">
                        <span class="footer-text">还没有账户？</span>
                        <a href="#" class="register-link">立即注册</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-panel">
        <div class="info-title">登录页面设计特点</div>
        <ul class="info-list">
            <li>渐变背景营造科技感氛围</li>
            <li>浮动装饰元素增加页面活力</li>
            <li>毛玻璃效果的现代化表单</li>
            <li>流畅的动画和交互反馈</li>
            <li>多种登录方式支持</li>
            <li>响应式设计适配各种设备</li>
        </ul>
    </div>
</body>
</html>
