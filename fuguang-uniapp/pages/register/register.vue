<template>
  <view class="register-container">
    <view class="header">
      <text class="title">注册账号</text>
      <text class="subtitle">加入浮光壁垒，开启任务之旅</text>
    </view>
    
    <view class="form-container">
      <view class="input-group">
        <u-input 
          v-model="form.username" 
          placeholder="请输入用户名"
          prefix-icon="account"
          :clearable="true"
        />
        <text class="tip">用户名长度为3-20个字符</text>
      </view>
      
      <view class="input-group">
        <u-input 
          v-model="form.password" 
          type="password"
          placeholder="请输入密码"
          prefix-icon="lock"
          :clearable="true"
        />
        <text class="tip">密码长度为6-20个字符</text>
      </view>
      
      <view class="input-group">
        <u-input 
          v-model="confirmPassword" 
          type="password"
          placeholder="请确认密码"
          prefix-icon="lock"
          :clearable="true"
        />
      </view>
      
      <view class="input-group">
        <u-input 
          v-model="form.phonenumber" 
          placeholder="请输入手机号（可选）"
          prefix-icon="phone"
          :clearable="true"
        />
      </view>
      
      <view class="input-group">
        <u-input 
          v-model="form.email" 
          placeholder="请输入邮箱（可选）"
          prefix-icon="email"
          :clearable="true"
        />
      </view>
      
      <view class="agreement-row">
        <u-checkbox v-model="agreed" :size="28">
          我已阅读并同意
        </u-checkbox>
        <text class="link" @click="showAgreement('user')">《用户协议》</text>
        <text>和</text>
        <text class="link" @click="showAgreement('privacy')">《隐私政策》</text>
      </view>
      
      <u-button 
        type="primary" 
        :loading="loading"
        :disabled="!canRegister"
        @click="handleRegister"
        class="register-btn"
      >
        注册
      </u-button>
      
      <view class="footer-links">
        <text class="link" @click="goLogin">已有账号？立即登录</text>
      </view>
    </view>
  </view>
</template>

<script>
import { register } from '@/api/auth'
import { validatePhone, validateEmail } from '@/utils/common'

export default {
  data() {
    return {
      form: {
        username: '',
        password: '',
        phonenumber: '',
        email: ''
      },
      confirmPassword: '',
      agreed: false,
      loading: false
    }
  },
  
  computed: {
    canRegister() {
      return this.form.username && 
             this.form.password && 
             this.confirmPassword &&
             this.form.password === this.confirmPassword &&
             this.agreed && 
             !this.loading
    }
  },
  
  methods: {
    async handleRegister() {
      if (!this.canRegister) return
      
      // 验证用户名长度
      if (this.form.username.length < 3 || this.form.username.length > 20) {
        uni.showToast({
          title: '用户名长度为3-20个字符',
          icon: 'none'
        })
        return
      }
      
      // 验证密码长度
      if (this.form.password.length < 6 || this.form.password.length > 20) {
        uni.showToast({
          title: '密码长度为6-20个字符',
          icon: 'none'
        })
        return
      }
      
      // 验证手机号格式（如果填写了）
      if (this.form.phonenumber && !validatePhone(this.form.phonenumber)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      // 验证邮箱格式（如果填写了）
      if (this.form.email && !validateEmail(this.form.email)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      try {
        await register(this.form)
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        // 跳转到登录页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        
      } catch (error) {
        console.error('注册失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    goLogin() {
      uni.navigateBack()
    },
    
    showAgreement(type) {
      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'
      uni.navigateTo({ url })
    }
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 40rpx;
  padding-top: 100rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
  
  .tip {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
    margin-left: 20rpx;
  }
}

.agreement-row {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  font-size: 26rpx;
  color: #666;
  
  .link {
    color: #3cc51f;
    margin: 0 8rpx;
  }
}

.register-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.footer-links {
  text-align: center;
  
  .link {
    color: #3cc51f;
    font-size: 28rpx;
  }
}
</style>
