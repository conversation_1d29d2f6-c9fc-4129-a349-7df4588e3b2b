// 认证相关API
import { post, get } from '@/utils/request'

// 用户登录
export const login = (data) => {
  return post('/app/login', data)
}

// 用户注册
export const register = (data) => {
  return post('/app/register', data)
}

// 获取用户信息
export const getUserInfo = () => {
  return get('/app/user/profile')
}

// 修改用户信息
export const updateUserInfo = (data) => {
  return post('/app/user/profile', data)
}

// 修改密码
export const updatePassword = (data) => {
  return post('/app/user/updatePwd', data)
}

// 上传头像
export const uploadAvatar = (filePath) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token')
    
    uni.uploadFile({
      url: 'http://localhost:8888/app/user/avatar',
      filePath,
      name: 'avatarfile',
      header: {
        Authorization: token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code === 200) {
          resolve(data)
        } else {
          uni.showToast({
            title: data.msg || '上传失败',
            icon: 'none'
          })
          reject(data)
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 实名认证
export const realNameAuth = (data) => {
  return post('/app/user/auth', data)
}

// 更新位置信息
export const updateLocation = (data) => {
  return post('/app/user/location', data)
}
