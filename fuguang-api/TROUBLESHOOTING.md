# 浮光壁垒项目问题解决指南

## 常见问题及解决方案

### 1. Bean 名称冲突问题

**问题描述**:

```
ConflictingBeanDefinitionException: Annotation-specified bean name 'appTaskController' for bean class...
```

**原因**: Spring 检测到多个同名的 Bean 定义

**解决方案**:
我们已经为所有 Controller 添加了明确的 Bean 名称：

- APP 端 Controller 使用 `@RestController("xxxApiController")`
- 管理端 Controller 使用 `@RestController("xxxManageController")`

### 1.1. bCryptPasswordEncoder Bean 冲突

**问题描述**:

```
The bean 'bCryptPasswordEncoder', defined in class path resource [com/ruoyi/app/config/AppSecurityConfig.class], could not be registered. A bean with that name has already been defined in class path resource [com/ruoyi/framework/config/SecurityConfig.class] and overriding is disabled.
```

**解决方案**:

1. 删除 APP 安全配置中重复的`bCryptPasswordEncoder` Bean 定义
2. 直接注入框架中已有的`bCryptPasswordEncoder`
3. 为 APP 的`AuthenticationManager`添加`@Qualifier("appAuthenticationManager")`注解

### 1.2. UserDetailsService Bean 冲突

**问题描述**:

```
Field userDetailsService in com.ruoyi.framework.config.SecurityConfig required a single bean, but 2 were found:
- userDetailsServiceImpl: defined in file [...]
- appUserDetailsService: defined in file [...]
```

**解决方案**:

1. 在框架的`SecurityConfig`中使用`@Qualifier("userDetailsServiceImpl")`指定使用系统的 UserDetailsService
2. 在 APP 的`AppSecurityConfig`中使用`@Qualifier("appUserDetailsService")`指定使用 APP 的 UserDetailsService
3. 为两个安全配置添加`@Order`注解，APP 配置优先级更高(`@Order(1)`)，框架配置次之(`@Order(2)`)
4. 限制 APP 安全配置只处理`/app/**`路径的请求

### 1.3. AuthenticationManager Bean 冲突

**问题描述**:

```
Found 2 beans for type interface org.springframework.security.authentication.AuthenticationManager, but none marked as primary
```

**解决方案**:

1. 为框架的`AuthenticationManager`添加`@Primary`注解，使其成为默认 Bean
2. 将 APP 安全配置从`WebSecurityConfigurerAdapter`风格更新为新的`SecurityFilterChain`风格
3. 为 APP 的`AuthenticationManager`使用明确的 Bean 名称`appAuthenticationManager`
4. 确保两个安全配置使用一致的 Spring Security 配置风格

### 1.4. Spring Security 版本兼容性问题

**问题描述**:

```
java: 找不到符号
  符号:   方法 securityMatcher(java.lang.String)
  位置: 类型为org.springframework.security.config.annotation.web.builders.HttpSecurity
```

**原因**: 项目使用 Spring Security 5.7.12，但使用了 6.x 版本的 API

**解决方案**:

1. 使用`requestMatchers().antMatchers("/app/**").and()`替代`securityMatcher("/app/**")`
2. 确保所有配置方法与 Spring Security 5.7.12 版本兼容
3. 保持 lambda 风格配置，但使用正确的方法名

### 2. 数据库连接问题

**问题描述**: 数据库连接失败

**检查项目**:

1. 确认数据库服务器是否可访问
2. 检查数据库名称、用户名、密码是否正确
3. 确认数据库表是否已创建

**配置文件**: `ruoyi-admin/src/main/resources/application-druid.yml`

### 3. Mapper 文件找不到

**问题描述**:

```
Invalid bound statement (not found): com.ruoyi.fuguang.mapper.AppUserMapper.selectAppUserList
```

**解决方案**:
确保 Mapper XML 文件位于正确位置：

- `ruoyi-fuguang/src/main/resources/mapper/fuguang/`

### 4. 依赖注入失败

**问题描述**:

```
NoSuchBeanDefinitionException: No qualifying bean of type...
```

**解决方案**:

1. 确保 Service 类添加了 `@Service` 注解
2. 确保 Mapper 接口添加了 `@Mapper` 注解
3. 检查包扫描路径是否正确

## 启动步骤

### 1. 数据库准备

```sql
-- 创建数据库
CREATE DATABASE fuguang DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 导入数据表
source sql/fuguang_app.sql;
```

### 2. 配置修改

修改 `application-druid.yml` 中的数据库连接信息：

```yaml
url: ************************************************************************************************************************************************
username: your-username
password: your-password
```

### 3. 编译项目

```bash
mvn clean compile -DskipTests
```

### 4. 启动项目

```bash
cd ruoyi-admin
mvn spring-boot:run
```

## 验证项目启动

### 1. 检查日志

启动成功后应该看到类似日志：

```
Started RuoYiApplication in xxx seconds
```

### 2. 访问管理后台

- URL: http://localhost:8888
- 默认账号: admin/admin123

### 3. 测试 APP 接口

```bash
# 测试首页接口
curl http://localhost:8888/app/home/<USER>

# 测试登录接口
curl -X POST http://localhost:8888/app/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"123456"}'
```

## 开发环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis (可选)

## 项目结构说明

```
fuguang-api/
├── ruoyi-admin/          # 管理后台启动模块
├── ruoyi-app/            # APP接口模块
├── ruoyi-fuguang/        # 业务逻辑模块
├── ruoyi-framework/      # 框架核心
├── ruoyi-system/         # 系统管理
├── ruoyi-common/         # 通用工具
└── sql/                  # 数据库脚本
```

## 联系支持

如果遇到其他问题，请检查：

1. 控制台错误日志
2. 数据库连接状态
3. 端口占用情况
4. 防火墙设置
