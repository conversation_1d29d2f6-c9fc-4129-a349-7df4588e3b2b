# 浮光壁垒项目快速启动指南

## 🚀 快速启动步骤

### 1. 环境准备
确保您的环境满足以下要求：
- ✅ JDK 1.8+
- ✅ Maven 3.6+
- ✅ MySQL 5.7+
- ✅ Redis (可选)

### 2. 数据库配置

#### 2.1 创建数据库
```sql
CREATE DATABASE fuguang DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

#### 2.2 导入数据表
```bash
mysql -u your_username -p fuguang < sql/fuguang_app.sql
```

#### 2.3 修改数据库连接
编辑 `ruoyi-admin/src/main/resources/application-druid.yml`：
```yaml
spring:
    datasource:
        druid:
            master:
                url: ************************************************************************************************************************************************
                username: your_username
                password: your_password
```

### 3. 编译项目
```bash
cd fuguang-api
mvn clean compile -DskipTests
```

### 4. 启动项目
```bash
cd ruoyi-admin
mvn spring-boot:run
```

### 5. 验证启动

#### 5.1 检查控制台日志
看到以下日志表示启动成功：
```
Started RuoYiApplication in xxx seconds
```

#### 5.2 访问管理后台
- 🌐 URL: http://localhost:8888
- 👤 默认账号: `admin`
- 🔑 默认密码: `admin123`

#### 5.3 测试APP接口
```bash
# 测试首页接口
curl http://localhost:8888/app/home/<USER>

# 测试用户注册
curl -X POST http://localhost:8888/app/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "phonenumber": "13800138000"
  }'

# 测试用户登录
curl -X POST http://localhost:8888/app/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

## 📱 APP接口说明

### 认证接口
- `POST /app/login` - 用户登录
- `POST /app/register` - 用户注册

### 首页接口
- `GET /app/home/<USER>
- `GET /app/home/<USER>
- `GET /app/home/<USER>

### 任务接口
- `GET /app/task/list` - 获取任务列表
- `GET /app/task/{id}` - 获取任务详情
- `POST /app/task` - 发布任务
- `POST /app/task/accept/{id}` - 接取任务

### 用户接口
- `GET /app/user/profile` - 获取用户信息
- `PUT /app/user/profile` - 更新用户信息
- `POST /app/user/avatar` - 上传头像

## 🛠️ 管理后台接口

### APP用户管理
- `GET /fuguang/appuser/list` - 用户列表
- `POST /fuguang/appuser` - 新增用户
- `PUT /fuguang/appuser` - 修改用户

### APP任务管理
- `GET /fuguang/task/list` - 任务列表
- `PUT /fuguang/task/audit` - 任务审核
- `PUT /fuguang/task/complete/{id}` - 强制完成任务

### APP配置管理
- `GET /fuguang/config/list` - 配置列表
- `POST /fuguang/config` - 新增配置
- `PUT /fuguang/config` - 修改配置

## ❗ 常见问题

### Bean冲突问题
如果遇到Bean名称冲突，请参考 `TROUBLESHOOTING.md` 文档。

### 数据库连接失败
1. 检查数据库服务是否启动
2. 确认连接信息是否正确
3. 检查防火墙设置

### 端口占用
如果8888端口被占用，可以修改 `application.yml` 中的端口配置：
```yaml
server:
  port: 8889
```

## 📞 技术支持

如果遇到问题，请：
1. 查看控制台错误日志
2. 参考 `TROUBLESHOOTING.md` 文档
3. 检查数据库和网络连接

## 🎯 下一步

项目启动成功后，您可以：
1. 🖥️ 登录管理后台配置系统参数
2. 📱 使用UniApp前端项目连接API
3. 🔧 根据需求自定义业务逻辑
4. 📊 查看数据库中的测试数据

祝您使用愉快！🎉
