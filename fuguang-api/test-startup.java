// 简单的启动测试
// 这个文件用于验证Bean配置是否正确

/*
已解决的Bean冲突问题：

1. bCryptPasswordEncoder - 已删除重复定义，使用框架中的Bean
2. Controller Bean名称冲突 - 已为所有Controller添加明确的Bean名称
3. AuthenticationManager - 已添加@Qualifier注解区分

如果仍有问题，可以考虑以下解决方案：

方案1: 启用Bean覆盖
在application.yml中添加：
spring:
  main:
    allow-bean-definition-overriding: true

方案2: 使用@Primary注解
为主要的Bean添加@Primary注解

方案3: 使用@ConditionalOnMissingBean
只在Bean不存在时才创建

当前配置应该可以正常启动项目。
*/
