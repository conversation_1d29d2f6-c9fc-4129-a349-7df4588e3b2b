package com.ruoyi.app.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.app.service.AppLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "APP登录接口")
@RestController("appLoginApiController")
@RequestMapping("/app")
public class AppLoginController
{
    @Autowired
    private AppLoginService appLoginService;

    /**
     * APP登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation("APP用户登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        // 生成令牌
        String token = appLoginService.login(loginBody.getUsername(), loginBody.getPassword());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        return ajax;
    }

    /**
     * APP注册方法
     * 
     * @param loginBody 注册信息
     * @return 结果
     */
    @ApiOperation("APP用户注册")
    @PostMapping("/register")
    public AjaxResult register(@RequestBody LoginBody loginBody)
    {
        String msg = appLoginService.register(loginBody.getUsername(), loginBody.getPassword());
        return AjaxResult.success(msg);
    }
}
