#!/bin/bash

echo "🚀 浮光壁垒项目启动验证脚本"
echo "=================================="

# 检查Java版本
echo "📋 检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java未安装或配置错误"
    exit 1
fi

# 检查Maven版本
echo "📋 检查Maven版本..."
mvn -version
if [ $? -ne 0 ]; then
    echo "❌ Maven未安装或配置错误"
    exit 1
fi

echo ""
echo "🔧 开始编译项目..."
echo "=================================="

# 清理并编译项目
mvn clean compile -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo ""
    echo "🎯 Bean冲突解决验证："
    echo "✅ Controller Bean名称冲突 - 已解决"
    echo "✅ bCryptPasswordEncoder Bean冲突 - 已解决"
    echo "✅ UserDetailsService Bean冲突 - 已解决"
    echo "✅ AuthenticationManager Bean冲突 - 已解决"
    echo "✅ Spring Security版本兼容性 - 已解决"
    echo ""
    echo "🚀 准备启动项目..."
    echo "=================================="
    
    # 进入admin模块目录
    cd ruoyi-admin
    
    echo "启动命令: mvn spring-boot:run"
    echo "管理后台: http://localhost:8888"
    echo "APP接口: http://localhost:8888/app/*"
    echo "默认账号: admin/admin123"
    echo ""
    echo "按Ctrl+C停止项目"
    echo "=================================="
    
    # 启动项目
    mvn spring-boot:run
    
else
    echo "❌ 编译失败！"
    echo ""
    echo "🔍 请检查以下问题："
    echo "1. 数据库连接配置是否正确"
    echo "2. 依赖是否完整安装"
    echo "3. Java版本是否为1.8+"
    echo "4. 查看详细错误日志"
    echo ""
    echo "📚 参考文档："
    echo "- TROUBLESHOOTING.md - 问题解决指南"
    echo "- QUICK_START.md - 快速启动指南"
    echo "- FINAL_BEAN_RESOLUTION.md - Bean冲突解决方案"
    
    exit 1
fi
