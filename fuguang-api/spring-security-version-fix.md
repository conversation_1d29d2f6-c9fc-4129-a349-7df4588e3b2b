# Spring Security版本兼容性修复

## 🔍 问题描述

在编译APP安全配置时遇到错误：
```
java: 找不到符号
  符号:   方法 securityMatcher(java.lang.String)
  位置: 类型为org.springframework.security.config.annotation.web.builders.HttpSecurity
```

## 📋 版本信息

项目使用的版本：
- Spring Boot: 2.5.15
- Spring Security: 5.7.12
- Spring Framework: 5.3.39

## ✅ 解决方案

### 问题原因
`securityMatcher()` 方法是Spring Security 6.x中引入的新方法，在5.7.12版本中不存在。

### 修复方法
使用Spring Security 5.7.12兼容的方法：

**错误的写法 (Spring Security 6.x)**:
```java
return httpSecurity
    .securityMatcher("/app/**")  // ❌ 在5.7.12中不存在
    .csrf(csrf -> csrf.disable())
    // ...
```

**正确的写法 (Spring Security 5.7.12)**:
```java
return httpSecurity
    .requestMatchers().antMatchers("/app/**").and()  // ✅ 5.7.12兼容
    .csrf(csrf -> csrf.disable())
    // ...
```

## 🔧 完整的修复后配置

```java
@Bean("appSecurityFilterChain")
public SecurityFilterChain appSecurityFilterChain(HttpSecurity httpSecurity) throws Exception {
    return httpSecurity
        // 只处理APP相关的请求 - 使用5.7.12兼容方法
        .requestMatchers().antMatchers("/app/**").and()
        // CSRF禁用
        .csrf(csrf -> csrf.disable())
        // 认证失败处理类
        .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
        // 基于token，所以不需要session
        .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        // 过滤请求
        .authorizeHttpRequests(requests -> {
            requests
                // 允许匿名访问的路径
                .antMatchers("/app/login", "/app/register", "/app/captchaImage").permitAll()
                // APP相关的其他请求需要认证
                .antMatchers("/app/**").authenticated();
        })
        // 添加Logout filter
        .logout(logout -> logout.logoutUrl("/app/logout").logoutSuccessHandler(logoutSuccessHandler))
        // 添加JWT filter
        .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
        // 添加CORS filter
        .addFilterBefore(corsFilter, JwtAuthenticationTokenFilter.class)
        .addFilterBefore(corsFilter, LogoutFilter.class)
        .build();
}
```

## 📚 Spring Security版本差异

### Spring Security 5.7.x
- 使用 `requestMatchers().antMatchers("/path/**").and()`
- 支持lambda风格配置
- 使用 `authorizeHttpRequests()` 替代 `authorizeRequests()`

### Spring Security 6.x
- 引入 `securityMatcher("/path/**")`
- 使用 `requestMatchers("/path/**")` 替代 `antMatchers()`
- 更多的lambda风格改进

## ✅ 验证修复

编译项目验证修复：
```bash
cd fuguang-api
mvn clean compile -DskipTests
```

应该不再出现编译错误。

## 🎯 最佳实践

1. **版本一致性**: 确保所有Spring Security配置使用相同的API风格
2. **向后兼容**: 在升级Spring Security版本时，注意API变化
3. **测试验证**: 修改配置后及时编译验证

修复完成！现在APP安全配置与项目的Spring Security版本完全兼容。
