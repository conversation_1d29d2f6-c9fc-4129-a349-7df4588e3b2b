# 浮光壁垒APP项目说明

## 项目概述

浮光壁垒APP项目是基于若依框架开发的移动端应用后端系统，包含APP用户管理、任务发布与接取、通知系统等核心功能。

## 项目架构

### 模块结构
```
fuguang-api/
├── ruoyi-admin/          # 管理后台模块（原有）
├── ruoyi-framework/      # 核心框架模块（原有）
├── ruoyi-system/         # 系统管理模块（原有）
├── ruoyi-common/         # 通用工具模块（原有）
├── ruoyi-generator/      # 代码生成模块（原有）
├── ruoyi-quartz/         # 定时任务模块（原有）
├── ruoyi-fuguang/        # 浮光壁垒业务逻辑模块（新增）
└── ruoyi-app/            # APP接口模块（新增）
```

### 新增模块说明

#### ruoyi-fuguang（业务逻辑模块）
- **功能**：处理APP和WEB共用的业务逻辑
- **主要内容**：
  - APP用户管理（AppUser）
  - 任务管理（AppTask）
  - 通知管理（AppNotice）
  - 配置管理（AppConfig）
  - 对应的Service、Mapper接口和实现

#### ruoyi-app（APP接口模块）
- **功能**：处理APP端的所有请求接口
- **主要内容**：
  - APP登录注册（AppLoginController）
  - APP首页功能（AppHomeController）
  - APP任务操作（AppTaskController）
  - APP用户信息（AppUserController）
  - APP专用认证服务（AppUserDetailsServiceImpl）

## 数据库设计

### 新增数据表

1. **app_user** - APP用户表
   - 存储APP用户信息，与系统用户表隔离
   - 支持实名认证、位置信息等APP特有字段

2. **app_task** - APP任务表
   - 任务发布、接取、完成等状态管理
   - 支持地理位置、热度评分等功能

3. **app_notice** - APP通知表
   - 系统通知、活动通知、任务通知
   - 支持全员通知和定向通知

4. **app_config** - APP配置表
   - 隐私协议、用户协议、首页标语等配置
   - 支持动态配置管理

5. **app_function** - APP功能配置表
   - 更多功能模块的配置管理

6. **app_agriculture** - 兴业助农配置表
   - 助农板块的内容配置

## 核心功能

### APP端功能

#### 1. 用户认证
- 密码登录
- 用户注册
- 游客模式支持
- JWT Token认证

#### 2. 首页功能
- 首页标语显示："链接你我，共创未来"
- 当前定位地址显示
- 搜索功能（任务、商品、商家）
- 二维码扫描
- 系统通知滚动播放
- 兴业助农板块
- 购物专区
- 更多功能配置
- 热门任务列表（最近十条）

#### 3. 任务管理
- 任务发布
- 任务浏览（自动增加浏览次数）
- 任务接取
- 任务完成
- 任务取消
- 我的发布任务
- 我的接取任务

#### 4. 用户管理
- 个人信息管理
- 头像上传
- 密码修改
- 实名认证
- 位置信息更新

### 管理后台功能

#### 1. APP用户管理
- 用户列表查询
- 用户信息编辑
- 用户状态管理
- 密码重置

#### 2. APP任务管理
- 任务列表管理
- 任务审核
- 强制完成/取消任务

#### 3. APP通知管理
- 通知发布
- 通知管理
- 系统通知发送

#### 4. APP配置管理
- 隐私协议配置
- 用户协议配置
- 首页标语配置
- 客服电话配置

## 技术特点

### 1. 架构设计
- 基于若依框架，保持编码风格一致
- APP和WEB请求完全分离
- 业务逻辑层共用，接口层独立

### 2. 安全认证
- APP独立的用户认证体系
- JWT Token管理
- 密码加密存储
- 权限控制

### 3. 数据隔离
- APP用户与系统用户完全隔离
- 独立的数据表设计
- 支持不同的业务场景

## 部署说明

### 1. 数据库初始化
```sql
-- 执行数据库脚本
source fuguang-api/sql/fuguang_app.sql
```

### 2. 配置文件
- 数据库连接配置
- Redis配置
- 文件上传路径配置

### 3. 启动项目
```bash
cd fuguang-api
mvn clean install
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

## API接口

### APP端接口前缀：`/app`
- `/app/login` - 登录
- `/app/register` - 注册
- `/app/home/<USER>
- `/app/task/*` - 任务相关
- `/app/user/*` - 用户相关

### 管理端接口前缀：`/fuguang`
- `/fuguang/appuser/*` - APP用户管理
- `/fuguang/task/*` - 任务管理
- `/fuguang/notice/*` - 通知管理
- `/fuguang/config/*` - 配置管理

## 开发规范

1. 遵循若依框架的编码规范
2. 使用统一的返回值格式（AjaxResult）
3. 统一的异常处理
4. 完善的日志记录
5. 接口文档使用Swagger

## 后续扩展

1. 支付系统集成
2. 地图服务集成
3. 推送服务
4. 商城功能
5. 评价系统
6. 消息聊天功能

## 注意事项

1. 项目基于若依3.9.0版本开发
2. 需要Java 8+环境
3. 需要MySQL 5.7+数据库
4. 需要Redis缓存服务
5. 建议使用Maven 3.6+进行构建
